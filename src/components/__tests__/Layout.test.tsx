/**
 * 布局组件测试
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

import MainLayout from '@layouts/MainLayout';
import TopNavigation from '@layouts/MainLayout/TopNavigation';
import authSlice from '@store/slices/authSlice';
import appSlice from '@store/slices/appSlice';

// 创建测试store
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authSlice,
      app: appSlice,
    },
    preloadedState: {
      auth: {
        isAuthenticated: true,
        user: null,
        token: 'test-token',
        loading: false,
        error: null,
      },
      app: {
        theme: 'dark',
        language: 'zh-CN',
        sidebarCollapsed: false,
        loading: false,
      },
    },
  });
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const store = createTestStore();
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </Provider>
  );
};

describe('Layout Components', () => {
  describe('TopNavigation', () => {
    it('应该渲染导航栏', () => {
      render(
        <TestWrapper>
          <TopNavigation />
        </TestWrapper>
      );
      
      expect(screen.getByText('FinSight')).toBeInTheDocument();
      expect(screen.getByText('快讯')).toBeInTheDocument();
      expect(screen.getByText('日历')).toBeInTheDocument();
      expect(screen.getByText('头条')).toBeInTheDocument();
      expect(screen.getByText('专题')).toBeInTheDocument();
      expect(screen.getByText('设置')).toBeInTheDocument();
    });

    it('不应该显示报表分析菜单项', () => {
      render(
        <TestWrapper>
          <TopNavigation />
        </TestWrapper>
      );
      
      expect(screen.queryByText('报表分析')).not.toBeInTheDocument();
    });

    it('不应该显示首页菜单项', () => {
      render(
        <TestWrapper>
          <TopNavigation />
        </TestWrapper>
      );
      
      expect(screen.queryByText('首页')).not.toBeInTheDocument();
    });
  });

  describe('MainLayout', () => {
    it('应该渲染主布局', () => {
      render(
        <TestWrapper>
          <MainLayout />
        </TestWrapper>
      );
      
      expect(screen.getByText('FinSight')).toBeInTheDocument();
    });
  });
});
