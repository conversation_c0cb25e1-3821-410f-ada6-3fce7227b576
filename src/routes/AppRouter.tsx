/**
 * 应用路由配置
 */
import React, { Suspense, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';

import { ROUTES } from '@constants/index';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { restoreAuthState } from '@store/slices/authSlice';
import MainLayout from '@layouts/MainLayout';
import ProtectedRoute from './ProtectedRoute';

// 懒加载页面组件
const LoginPage = React.lazy(() => import('@pages/Login'));
const NewsFlash = React.lazy(() => import('@pages/NewsFlash'));
const Calendar = React.lazy(() => import('@pages/Calendar'));
const Headlines = React.lazy(() => import('@pages/Headlines'));
const Topics = React.lazy(() => import('@pages/Topics'));
const Settings = React.lazy(() => import('@pages/Settings'));

/**
 * 页面加载中组件
 */
const PageLoading: React.FC = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
    }}
  >
    <Spin size='large' />
  </div>
);

/**
 * 应用路由组件
 */
const AppRouter: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(state => state.auth);

  // 应用启动时恢复认证状态
  useEffect(() => {
    dispatch(restoreAuthState());
  }, [dispatch]);

  return (
    <Routes>
        {/* 登录页面 */}
        <Route
          path={ROUTES.LOGIN}
          element={
            isAuthenticated ? (
              <Navigate to={ROUTES.NEWS_FLASH} replace />
            ) : (
              <Suspense fallback={<PageLoading />}>
                <LoginPage />
              </Suspense>
            )
          }
        />

        {/* 需要认证的路由 */}
        <Route
          path='/'
          element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to={ROUTES.NEWS_FLASH} replace />} />
          <Route path={ROUTES.DASHBOARD} element={<Navigate to={ROUTES.NEWS_FLASH} replace />} />
          <Route path={ROUTES.NEWS_FLASH} element={
            <Suspense fallback={<PageLoading />}>
              <NewsFlash />
            </Suspense>
          } />
          <Route path={ROUTES.CALENDAR} element={
            <Suspense fallback={<PageLoading />}>
              <Calendar />
            </Suspense>
          } />
          <Route path={ROUTES.HEADLINES} element={
            <Suspense fallback={<PageLoading />}>
              <Headlines />
            </Suspense>
          } />
          <Route path={ROUTES.TOPICS} element={
            <Suspense fallback={<PageLoading />}>
              <Topics />
            </Suspense>
          } />
          <Route path={ROUTES.SETTINGS} element={
            <Suspense fallback={<PageLoading />}>
              <Settings />
            </Suspense>
          } />
        </Route>

        {/* 404 页面 - 重定向到登录或快讯 */}
        <Route
          path='*'
          element={
            <Navigate
              to={isAuthenticated ? ROUTES.NEWS_FLASH : ROUTES.LOGIN}
              replace
            />
          }
        />
    </Routes>
  );
};

export default AppRouter;
