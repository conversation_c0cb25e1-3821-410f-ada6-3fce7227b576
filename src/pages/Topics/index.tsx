/**
 * 专题页面
 */
import React, { useState } from 'react';
import { Card, Row, Col, Typography, Tag, Progress, Button, Space } from 'antd';
import {
  TagsOutlined,
  RiseOutlined,
  DollarOutlined,
  GlobalOutlined,
  BankOutlined,
  LineChartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

import { colors, spacing, typography, borderRadius, shadows } from '@styles/theme';

const { Title, Text, Paragraph } = Typography;

// 模拟专题数据
const mockTopics = [
  {
    id: 1,
    title: '美联储政策展望',
    description: '深度解析美联储货币政策走向，关注利率决议对市场的影响',
    category: '央行政策',
    icon: <BankOutlined />,
    color: colors.primary,
    articles: 15,
    readCount: 45200,
    updateTime: '2小时前',
    trend: 'up' as const,
    trendValue: '+12.5%',
    tags: ['美联储', '利率', '货币政策'],
    progress: 75,
  },
  {
    id: 2,
    title: '黄金投资策略',
    description: '黄金市场深度分析，投资机会与风险并存',
    category: '贵金属',
    icon: <DollarOutlined />,
    color: colors.warning,
    articles: 23,
    readCount: 38900,
    updateTime: '4小时前',
    trend: 'up' as const,
    trendValue: '+8.3%',
    tags: ['黄金', '投资', '避险'],
    progress: 85,
  },
  {
    id: 3,
    title: '全球经济复苏',
    description: '各国经济数据对比分析，复苏进程追踪',
    category: '宏观经济',
    icon: <GlobalOutlined />,
    color: colors.success,
    articles: 18,
    readCount: 29600,
    updateTime: '6小时前',
    trend: 'down' as const,
    trendValue: '-3.2%',
    tags: ['经济复苏', 'GDP', '就业'],
    progress: 60,
  },
  {
    id: 4,
    title: '科技股投资',
    description: '科技板块投资机会分析，关注AI、芯片等热门赛道',
    category: '股票市场',
    icon: <LineChartOutlined />,
    color: colors.info,
    articles: 31,
    readCount: 52100,
    updateTime: '1天前',
    trend: 'up' as const,
    trendValue: '+15.7%',
    tags: ['科技股', 'AI', '芯片'],
    progress: 90,
  },
  {
    id: 5,
    title: '原油市场动态',
    description: '原油价格走势分析，供需关系变化追踪',
    category: '能源',
    icon: <RiseOutlined />,
    color: colors.error,
    articles: 12,
    readCount: 21800,
    updateTime: '1天前',
    trend: 'up' as const,
    trendValue: '+6.8%',
    tags: ['原油', 'WTI', '供需'],
    progress: 45,
  },
  {
    id: 6,
    title: '数字货币趋势',
    description: '加密货币市场分析，监管政策影响评估',
    category: '数字货币',
    icon: <DollarOutlined />,
    color: colors.warning,
    articles: 27,
    readCount: 41300,
    updateTime: '2天前',
    trend: 'down' as const,
    trendValue: '-9.1%',
    tags: ['比特币', '以太坊', '监管'],
    progress: 70,
  },
];

const Container = styled.div`
  padding: 0;
  height: calc(100vh - 180px);
`;

const Header = styled.div`
  margin-bottom: ${spacing.lg};
`;

const TopicCard = styled(Card)`
  height: 100%;
  background: ${colors.background.card};
  border: 1px solid ${colors.border.default};
  border-radius: ${borderRadius.large};
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: ${colors.border.hover};
    box-shadow: ${shadows.card};
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: ${spacing.lg};
    height: 100%;
    display: flex;
    flex-direction: column;
  }
`;

const TopicIcon = styled.div<{ color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${borderRadius.medium};
  background: ${props => `${props.color}20`};
  color: ${props => props.color};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: ${spacing.md};
`;

const TopicTitle = styled.h3`
  color: ${colors.text.primary};
  font-size: ${typography.h3.fontSize};
  font-weight: ${typography.h3.fontWeight};
  margin: 0 0 ${spacing.xs} 0;
  line-height: 1.3;
`;

const TopicDescription = styled(Paragraph)`
  color: ${colors.text.secondary};
  font-size: ${typography.small.fontSize};
  margin: 0 0 ${spacing.md} 0;
  line-height: 1.5;
  flex: 1;
`;

const TopicStats = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};
  font-size: ${typography.small.fontSize};
  color: ${colors.text.tertiary};
`;

const TrendIndicator = styled.div<{ trend: 'up' | 'down' }>`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  color: ${props => props.trend === 'up' ? colors.success : colors.error};
  font-weight: 500;
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${spacing.xs};
  margin-bottom: ${spacing.md};
`;

const ProgressContainer = styled.div`
  margin-top: auto;
`;

const ProgressLabel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.xs};
  font-size: ${typography.small.fontSize};
  color: ${colors.text.secondary};
`;

/**
 * 专题页面组件
 */
const Topics: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { key: 'all', label: '全部专题' },
    { key: '央行政策', label: '央行政策' },
    { key: '贵金属', label: '贵金属' },
    { key: '宏观经济', label: '宏观经济' },
    { key: '股票市场', label: '股票市场' },
    { key: '能源', label: '能源' },
    { key: '数字货币', label: '数字货币' },
  ];

  const filteredTopics = selectedCategory === 'all' 
    ? mockTopics 
    : mockTopics.filter(topic => topic.category === selectedCategory);

  const renderTrendIcon = (trend: 'up' | 'down') => {
    return trend === 'up' ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
  };

  return (
    <Container>
      <Header>
        <Title 
          level={2} 
          style={{ 
            color: colors.text.primary, 
            margin: 0,
            fontSize: typography.h2.fontSize,
            fontWeight: typography.h2.fontWeight,
          }}
        >
          <TagsOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
          财经专题
        </Title>
        <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
          深度专题分析，把握投资机会
        </Text>
      </Header>

      <div style={{ marginBottom: spacing.lg }}>
        <Space wrap>
          {categories.map(category => (
            <Button
              key={category.key}
              type={selectedCategory === category.key ? 'primary' : 'default'}
              onClick={() => setSelectedCategory(category.key)}
              style={{
                background: selectedCategory === category.key ? colors.primary : colors.background.secondary,
                borderColor: colors.border.default,
                color: selectedCategory === category.key ? colors.text.primary : colors.text.secondary,
              }}
            >
              {category.label}
            </Button>
          ))}
        </Space>
      </div>

      <div style={{ height: 'calc(100vh - 280px)', overflowY: 'auto' }}>
        <Row gutter={[16, 16]}>
          {filteredTopics.map((topic) => (
            <Col xs={24} sm={12} lg={8} key={topic.id}>
              <TopicCard>
                <TopicIcon color={topic.color}>
                  {topic.icon}
                </TopicIcon>

                <div style={{ marginBottom: spacing.xs }}>
                  <Tag 
                    style={{ 
                      backgroundColor: `${topic.color}20`, 
                      color: topic.color, 
                      border: `1px solid ${topic.color}40`,
                      fontSize: '10px',
                    }}
                  >
                    {topic.category}
                  </Tag>
                </div>

                <TopicTitle>{topic.title}</TopicTitle>

                <TopicDescription ellipsis={{ rows: 2 }}>
                  {topic.description}
                </TopicDescription>

                <TopicStats>
                  <span>{topic.articles} 篇文章</span>
                  <span>{topic.readCount.toLocaleString()} 阅读</span>
                  <span>{topic.updateTime}</span>
                </TopicStats>

                <div style={{ marginBottom: spacing.md }}>
                  <TrendIndicator trend={topic.trend}>
                    {renderTrendIcon(topic.trend)}
                    <span>{topic.trendValue}</span>
                    <span style={{ color: colors.text.tertiary }}>热度变化</span>
                  </TrendIndicator>
                </div>

                <TagContainer>
                  {topic.tags.map((tag, index) => (
                    <Tag 
                      key={index}
                      style={{ 
                        backgroundColor: colors.background.secondary, 
                        color: colors.text.secondary,
                        border: `1px solid ${colors.border.default}`,
                        fontSize: '10px',
                      }}
                    >
                      {tag}
                    </Tag>
                  ))}
                </TagContainer>

                <ProgressContainer>
                  <ProgressLabel>
                    <span>专题完成度</span>
                    <span>{topic.progress}%</span>
                  </ProgressLabel>
                  <Progress 
                    percent={topic.progress} 
                    showInfo={false}
                    strokeColor={topic.color}
                    trailColor={colors.background.secondary}
                    size="small"
                  />
                </ProgressContainer>
              </TopicCard>
            </Col>
          ))}
        </Row>
      </div>
    </Container>
  );
};

export default Topics;
