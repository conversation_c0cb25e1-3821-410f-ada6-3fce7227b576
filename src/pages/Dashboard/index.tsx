/**
 * 主页面 - 参考金十数据布局
 */
import React, { useState, useEffect } from 'react';
import { Card, Typography, Tag, Space, Badge, Button } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  ThunderboltOutlined,
  RiseOutlined,
  GlobalOutlined,
  BankOutlined,
  CalendarOutlined,
  FileTextOutlined,
  EyeOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

import {
  colors,
  typography,
  spacing,
  borderRadius,

} from '@styles/theme';

const { Title, Text } = Typography;

// 模拟快讯数据
const mockNewsFlash = [
  {
    id: 1,
    time: '22:10:04',
    content: '美联储主席鲍威尔：通胀数据显示价格压力正在缓解',
    importance: 'high',
    isNew: true,
  },
  {
    id: 2,
    time: '22:08:15',
    content: '黄金期货收盘上涨1.2%，报2045.50美元/盎司',
    importance: 'medium',
    isNew: true,
  },
  {
    id: 3,
    time: '22:05:32',
    content: '美国7月非农就业人数增加20.9万人，预期18.5万人',
    importance: 'high',
    isNew: false,
  },
  {
    id: 4,
    time: '22:03:18',
    content: '欧洲央行官员：预计年底前还将有一次降息',
    importance: 'medium',
    isNew: false,
  },
];

// 模拟头条数据
const mockHeadlines = [
  {
    id: 1,
    title: '美联储政策展望：通胀压力缓解但仍需谨慎',
    readCount: 15420,
    publishTime: '2小时前',
    category: '央行政策',
  },
  {
    id: 2,
    title: '黄金投资策略：突破关键阻力位后的机会与风险',
    readCount: 12350,
    publishTime: '3小时前',
    category: '贵金属',
  },
  {
    id: 3,
    title: '全球经济复苏进程：各国数据对比分析',
    readCount: 18760,
    publishTime: '5小时前',
    category: '宏观经济',
  },
];

// 模拟市场数据
const mockMarketData = [
  {
    name: '黄金',
    price: '2045.50',
    change: '+1.2%',
    trend: 'up',
    color: colors.warning,
  },
  {
    name: 'WTI原油',
    price: '78.45',
    change: '+2.1%',
    trend: 'up',
    color: colors.error,
  },
  {
    name: '美元指数',
    price: '103.25',
    change: '-0.3%',
    trend: 'down',
    color: colors.info,
  },
  {
    name: '比特币',
    price: '42,580',
    change: '+3.5%',
    trend: 'up',
    color: colors.warning,
  },
];

// 样式组件
const Container = styled.div`
  padding: 0;
  height: 100%;
`;

const MainContent = styled.div`
  display: flex;
  gap: ${spacing.lg};
  height: calc(100vh - 180px);
`;

const LeftPanel = styled.div`
  width: 350px;
  display: flex;
  flex-direction: column;
`;

const CenterPanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const RightPanel = styled.div`
  width: 320px;
  display: flex;
  flex-direction: column;
`;

const NewsItem = styled.div<{ importance: string; isNew: boolean }>`
  padding: ${spacing.sm};
  border-bottom: 1px solid ${colors.border.default};
  transition: all 0.3s ease;
  background: ${props => props.isNew ? 'rgba(79, 70, 229, 0.05)' : 'transparent'};
  border-left: ${props => {
    if (props.importance === 'high') return `3px solid ${colors.error}`;
    if (props.importance === 'medium') return `3px solid ${colors.warning}`;
    return `3px solid ${colors.border.default}`;
  }};

  &:hover {
    background: ${colors.background.secondary};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const MarketItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${spacing.sm};
  border-bottom: 1px solid ${colors.border.default};
  transition: all 0.3s ease;

  &:hover {
    background: ${colors.background.secondary};
  }

  &:last-child {
    border-bottom: none;
  }
`;

/**
 * 主页面组件 - 参考金十数据布局
 */
const Dashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { hour12: false });
  };

  return (
    <Container>
      {/* 顶部标题栏 */}
      <div style={{
        marginBottom: spacing.lg,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <div>
          <Title
            level={2}
            style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: typography.h2.fontSize,
              fontWeight: typography.h2.fontWeight,
            }}
          >
            FinSight 财经数据中心
          </Title>
          <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
            实时财经资讯 · 专业数据分析 · 投资决策支持
          </Text>
        </div>
        <Space>
          <Badge
            count="LIVE"
            style={{
              backgroundColor: colors.error,
              color: colors.text.primary,
              fontWeight: 'bold',
            }}
          />
          <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
            交易时钟：{formatTime(currentTime)}
          </Text>
        </Space>
      </div>

      {/* 主要内容区域 - 三栏布局 */}
      <MainContent>
        {/* 左侧快讯面板 */}
        <LeftPanel>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', color: colors.text.primary }}>
                <ThunderboltOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
                市场快讯
                <Badge
                  count="LIVE"
                  style={{
                    backgroundColor: colors.error,
                    marginLeft: spacing.sm,
                    fontSize: '10px',
                  }}
                />
              </div>
            }
            style={{
              background: colors.background.card,
              border: `1px solid ${colors.border.default}`,
              borderRadius: borderRadius.large,
              height: '100%',
            }}
            bodyStyle={{ padding: 0, height: 'calc(100% - 60px)' }}
          >
            <div style={{ height: '100%', overflowY: 'auto' }}>
              {mockNewsFlash.map((item) => (
                <NewsItem
                  key={item.id}
                  importance={item.importance}
                  isNew={item.isNew}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: spacing.xs }}>
                    <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
                      {item.time}
                    </Text>
                    {item.isNew && (
                      <Badge
                        count="NEW"
                        style={{
                          backgroundColor: colors.primary,
                          fontSize: '8px',
                          height: '14px',
                          lineHeight: '14px',
                        }}
                      />
                    )}
                  </div>
                  <Text style={{
                    color: colors.text.primary,
                    fontSize: typography.small.fontSize,
                    lineHeight: 1.4,
                  }}>
                    {item.content}
                  </Text>
                </NewsItem>
              ))}
            </div>
          </Card>
        </LeftPanel>

        {/* 中间主要内容面板 */}
        <CenterPanel>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', color: colors.text.primary }}>
                <FileTextOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
                财经头条
              </div>
            }
            style={{
              background: colors.background.card,
              border: `1px solid ${colors.border.default}`,
              borderRadius: borderRadius.large,
              height: '100%',
            }}
            bodyStyle={{ padding: spacing.lg, height: 'calc(100% - 60px)' }}
          >
            <div style={{ height: '100%', overflowY: 'auto' }}>
              {mockHeadlines.map((item) => (
                <div
                  key={item.id}
                  style={{
                    padding: spacing.md,
                    borderBottom: `1px solid ${colors.border.default}`,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = colors.background.secondary;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'transparent';
                  }}
                >
                  <div style={{ marginBottom: spacing.xs }}>
                    <Tag
                      style={{
                        backgroundColor: `${colors.primary}20`,
                        color: colors.primary,
                        border: `1px solid ${colors.primary}40`,
                        fontSize: '10px',
                      }}
                    >
                      {item.category}
                    </Tag>
                  </div>

                  <Title
                    level={4}
                    style={{
                      color: colors.text.primary,
                      margin: `0 0 ${spacing.xs} 0`,
                      fontSize: typography.body.fontSize,
                      fontWeight: 500,
                      lineHeight: 1.4,
                    }}
                  >
                    {item.title}
                  </Title>

                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    color: colors.text.tertiary,
                    fontSize: typography.small.fontSize,
                  }}>
                    <Space>
                      <ClockCircleOutlined />
                      <span>{item.publishTime}</span>
                    </Space>
                    <Space>
                      <EyeOutlined />
                      <span>{item.readCount.toLocaleString()}</span>
                    </Space>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </CenterPanel>

        {/* 右侧市场数据面板 */}
        <RightPanel>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', color: colors.text.primary }}>
                <RiseOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
                市场行情
              </div>
            }
            style={{
              background: colors.background.card,
              border: `1px solid ${colors.border.default}`,
              borderRadius: borderRadius.large,
              height: '100%',
            }}
            bodyStyle={{ padding: 0, height: 'calc(100% - 60px)' }}
          >
            <div style={{ height: '100%', overflowY: 'auto' }}>
              {mockMarketData.map((item, index) => (
                <MarketItem key={index}>
                  <div>
                    <div style={{
                      color: colors.text.primary,
                      fontSize: typography.body.fontSize,
                      fontWeight: 500,
                    }}>
                      {item.name}
                    </div>
                    <div style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                    }}>
                      ${item.price}
                    </div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{
                      color: item.trend === 'up' ? colors.success : colors.error,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                      display: 'flex',
                      alignItems: 'center',
                      gap: spacing.xs,
                    }}>
                      {item.trend === 'up' ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                      {item.change}
                    </div>
                  </div>
                </MarketItem>
              ))}

              {/* 快捷导航 */}
              <div style={{ padding: spacing.md, borderTop: `1px solid ${colors.border.default}` }}>
                <Title
                  level={5}
                  style={{
                    color: colors.text.primary,
                    margin: `0 0 ${spacing.sm} 0`,
                    fontSize: typography.small.fontSize,
                  }}
                >
                  快捷导航
                </Title>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="text"
                    icon={<CalendarOutlined />}
                    style={{
                      width: '100%',
                      textAlign: 'left',
                      color: colors.text.secondary,
                      justifyContent: 'flex-start',
                    }}
                  >
                    财经日历
                  </Button>
                  <Button
                    type="text"
                    icon={<GlobalOutlined />}
                    style={{
                      width: '100%',
                      textAlign: 'left',
                      color: colors.text.secondary,
                      justifyContent: 'flex-start',
                    }}
                  >
                    全球指数
                  </Button>
                  <Button
                    type="text"
                    icon={<BankOutlined />}
                    style={{
                      width: '100%',
                      textAlign: 'left',
                      color: colors.text.secondary,
                      justifyContent: 'flex-start',
                    }}
                  >
                    央行动态
                  </Button>
                </Space>
              </div>
            </div>
          </Card>
        </RightPanel>
      </MainContent>
    </Container>
  );
};

export default Dashboard;
