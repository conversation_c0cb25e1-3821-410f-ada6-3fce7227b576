/**
 * 头条页面
 */
import React, { useState } from 'react';
import { Card, Typography, Tag, Space, Avatar, Button, Tabs } from 'antd';
import { 
  FileTextOutlined, 
  EyeOutlined, 
  LikeOutlined, 
  ShareAltOutlined,
  ClockCircleOutlined,
  FireOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

import { colors, spacing, typography, borderRadius } from '@styles/theme';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

// 模拟头条数据
const mockHeadlines = {
  hot: [
    {
      id: 1,
      title: '美联储主席鲍威尔：通胀压力正在缓解，但仍需保持谨慎态度',
      summary: '鲍威尔在最新讲话中表示，虽然通胀数据显示价格压力正在缓解，但美联储仍需要保持谨慎态度，密切关注经济数据的变化...',
      author: '金融时报',
      publishTime: '2小时前',
      readCount: 15420,
      likeCount: 892,
      category: '央行政策',
      isHot: true,
      image: 'https://via.placeholder.com/120x80/333/fff?text=Fed',
    },
    {
      id: 2,
      title: '黄金价格突破关键阻力位，分析师看好后市表现',
      summary: '黄金期货价格今日突破2040美元关键阻力位，多位分析师表示看好黄金后市表现，预计将继续上涨...',
      author: '财经观察',
      publishTime: '3小时前',
      readCount: 12350,
      likeCount: 567,
      category: '贵金属',
      isHot: true,
      image: 'https://via.placeholder.com/120x80/f59e0b/fff?text=Gold',
    },
    {
      id: 3,
      title: '美国7月非农就业数据超预期，失业率维持低位',
      summary: '美国劳工部公布的7月非农就业数据显示，新增就业人数20.9万人，超过市场预期的18.5万人...',
      author: '经济日报',
      publishTime: '5小时前',
      readCount: 18760,
      likeCount: 1234,
      category: '经济数据',
      isHot: false,
      image: 'https://via.placeholder.com/120x80/10b981/fff?text=Jobs',
    },
  ],
  latest: [
    {
      id: 4,
      title: '欧洲央行官员暗示年底前可能再次降息',
      summary: '欧洲央行执委会成员在最新讲话中暗示，如果经济数据继续疲软，央行可能在年底前再次降息...',
      author: '路透社',
      publishTime: '1小时前',
      readCount: 8920,
      likeCount: 445,
      category: '央行政策',
      isHot: false,
      image: 'https://via.placeholder.com/120x80/3b82f6/fff?text=ECB',
    },
    {
      id: 5,
      title: '原油库存数据利好，WTI原油价格上涨2.1%',
      summary: 'API原油库存数据显示库存大幅下降，WTI原油价格应声上涨，涨幅达到2.1%...',
      author: '能源观察',
      publishTime: '2小时前',
      readCount: 6780,
      likeCount: 321,
      category: '能源',
      isHot: false,
      image: 'https://via.placeholder.com/120x80/ef4444/fff?text=Oil',
    },
  ],
};

const Container = styled.div`
  padding: 0;
  height: calc(100vh - 180px);
`;

const Header = styled.div`
  margin-bottom: ${spacing.lg};
`;

const NewsCard = styled(Card)`
  margin-bottom: ${spacing.md};
  background: ${colors.background.card};
  border: 1px solid ${colors.border.default};
  border-radius: ${borderRadius.medium};
  transition: all 0.3s ease;

  &:hover {
    border-color: ${colors.border.hover};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .ant-card-body {
    padding: ${spacing.lg};
  }
`;

const NewsContent = styled.div`
  display: flex;
  gap: ${spacing.md};
`;

const NewsImage = styled.img`
  width: 120px;
  height: 80px;
  border-radius: ${borderRadius.small};
  object-fit: cover;
  flex-shrink: 0;
`;

const NewsInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const NewsTitle = styled.h3`
  color: ${colors.text.primary};
  font-size: ${typography.body.fontSize};
  font-weight: 600;
  margin: 0 0 ${spacing.xs} 0;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: ${colors.primary};
  }
`;

const NewsSummary = styled(Paragraph)`
  color: ${colors.text.secondary};
  font-size: ${typography.small.fontSize};
  margin: 0 0 ${spacing.sm} 0;
  line-height: 1.5;
`;

const NewsFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
`;

const NewsAuthor = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  color: ${colors.text.tertiary};
  font-size: ${typography.small.fontSize};
`;

const NewsStats = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  color: ${colors.text.tertiary};
  font-size: ${typography.small.fontSize};
`;

const HotBadge = styled.div`
  position: absolute;
  top: ${spacing.sm};
  right: ${spacing.sm};
  background: ${colors.error};
  color: ${colors.text.primary};
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 2px;
`;

/**
 * 头条页面组件
 */
const Headlines: React.FC = () => {
  const [activeTab, setActiveTab] = useState('hot');

  const renderNewsList = (newsList: typeof mockHeadlines.hot) => {
    return (
      <div>
        {newsList.map((news) => (
          <NewsCard key={news.id} style={{ position: 'relative' }}>
            {news.isHot && (
              <HotBadge>
                <FireOutlined />
                HOT
              </HotBadge>
            )}
            
            <NewsContent>
              <NewsImage src={news.image} alt={news.title} />
              
              <NewsInfo>
                <div style={{ marginBottom: spacing.xs }}>
                  <Tag 
                    style={{ 
                      backgroundColor: `${colors.primary}20`, 
                      color: colors.primary, 
                      border: `1px solid ${colors.primary}40`,
                      fontSize: '10px',
                    }}
                  >
                    {news.category}
                  </Tag>
                </div>
                
                <NewsTitle>{news.title}</NewsTitle>
                
                <NewsSummary ellipsis={{ rows: 2 }}>
                  {news.summary}
                </NewsSummary>
                
                <NewsFooter>
                  <NewsAuthor>
                    <Avatar size="small" style={{ backgroundColor: colors.primary }}>
                      {news.author.charAt(0)}
                    </Avatar>
                    <span>{news.author}</span>
                    <ClockCircleOutlined />
                    <span>{news.publishTime}</span>
                  </NewsAuthor>
                  
                  <NewsStats>
                    <Space>
                      <EyeOutlined />
                      <span>{news.readCount.toLocaleString()}</span>
                    </Space>
                    <Space>
                      <LikeOutlined />
                      <span>{news.likeCount}</span>
                    </Space>
                    <Button 
                      type="text" 
                      icon={<ShareAltOutlined />} 
                      size="small"
                      style={{ color: colors.text.tertiary }}
                    />
                  </NewsStats>
                </NewsFooter>
              </NewsInfo>
            </NewsContent>
          </NewsCard>
        ))}
      </div>
    );
  };

  return (
    <Container>
      <Header>
        <Title 
          level={2} 
          style={{ 
            color: colors.text.primary, 
            margin: 0,
            fontSize: typography.h2.fontSize,
            fontWeight: typography.h2.fontWeight,
          }}
        >
          <FileTextOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
          财经头条
        </Title>
        <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
          精选财经资讯，深度解读市场动态
        </Text>
      </Header>

      <Card
        style={{
          background: colors.background.card,
          border: `1px solid ${colors.border.default}`,
          borderRadius: borderRadius.large,
          height: 'calc(100vh - 200px)',
        }}
        bodyStyle={{ padding: spacing.lg, height: '100%' }}
      >
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          style={{ height: '100%' }}
          tabBarStyle={{ 
            borderBottom: `1px solid ${colors.border.default}`,
            marginBottom: spacing.lg,
          }}
        >
          <TabPane 
            tab={
              <span style={{ color: colors.text.primary }}>
                <FireOutlined />
                热门头条
              </span>
            } 
            key="hot"
          >
            <div style={{ height: 'calc(100% - 60px)', overflowY: 'auto' }}>
              {renderNewsList(mockHeadlines.hot)}
            </div>
          </TabPane>
          
          <TabPane 
            tab={
              <span style={{ color: colors.text.primary }}>
                <ClockCircleOutlined />
                最新资讯
              </span>
            } 
            key="latest"
          >
            <div style={{ height: 'calc(100% - 60px)', overflowY: 'auto' }}>
              {renderNewsList(mockHeadlines.latest)}
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </Container>
  );
};

export default Headlines;
