/**
 * 快讯页面
 */
import React, { useState, useEffect } from 'react';
import { Card, Tag, Typography, Space, Badge, Button } from 'antd';
import { 
  ThunderboltOutlined, 
  FireOutlined, 
  ClockCircleOutlined,
  SoundOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import styled from 'styled-components';

import { colors, spacing, typography, borderRadius } from '@styles/theme';

const { Title, Text } = Typography;

// 模拟快讯数据
const mockNewsData = [
  {
    id: 1,
    time: '22:10:04',
    content: '美联储主席鲍威尔：通胀数据显示价格压力正在缓解，但仍需保持谨慎',
    importance: 'high',
    category: '央行',
    isNew: true,
  },
  {
    id: 2,
    time: '22:08:15',
    content: '黄金期货收盘上涨1.2%，报2045.50美元/盎司',
    importance: 'medium',
    category: '贵金属',
    isNew: true,
  },
  {
    id: 3,
    time: '22:05:32',
    content: '美国7月非农就业人数增加20.9万人，预期18.5万人',
    importance: 'high',
    category: '经济数据',
    isNew: false,
  },
  {
    id: 4,
    time: '22:03:18',
    content: '欧洲央行官员：预计年底前还将有一次降息',
    importance: 'medium',
    category: '央行',
    isNew: false,
  },
  {
    id: 5,
    time: '22:01:45',
    content: '原油库存数据公布：美国原油库存减少280万桶',
    importance: 'medium',
    category: '能源',
    isNew: false,
  },
];

const Container = styled.div`
  padding: 0;
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.lg};
`;

const ControlPanel = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};
  margin-bottom: ${spacing.lg};
  padding: ${spacing.md};
  background: ${colors.background.card};
  border-radius: ${borderRadius.medium};
  border: 1px solid ${colors.border.default};
`;

const NewsContainer = styled.div`
  flex: 1;
  overflow: hidden;
`;

const NewsItem = styled.div<{ importance: string; isNew: boolean }>`
  padding: ${spacing.md};
  border-bottom: 1px solid ${colors.border.default};
  transition: all 0.3s ease;
  background: ${props => props.isNew ? 'rgba(79, 70, 229, 0.05)' : 'transparent'};
  border-left: ${props => {
    if (props.importance === 'high') return `4px solid ${colors.error}`;
    if (props.importance === 'medium') return `4px solid ${colors.warning}`;
    return `4px solid ${colors.border.default}`;
  }};

  &:hover {
    background: ${colors.background.secondary};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const TimeStamp = styled.span`
  color: ${colors.text.secondary};
  font-size: ${typography.small.fontSize};
  font-weight: 500;
`;

const NewsContent = styled.div`
  margin-top: ${spacing.xs};
  color: ${colors.text.primary};
  font-size: ${typography.body.fontSize};
  line-height: 1.6;
`;

const CategoryTag = styled(Tag)`
  margin-top: ${spacing.xs};
  border: none;
  background: ${colors.background.secondary};
  color: ${colors.text.secondary};
`;

const ImportanceIcon = styled.span<{ importance: string }>`
  color: ${props => {
    if (props.importance === 'high') return colors.error;
    if (props.importance === 'medium') return colors.warning;
    return colors.text.tertiary;
  }};
  margin-right: ${spacing.xs};
`;

/**
 * 快讯页面组件
 */
const NewsFlash: React.FC = () => {
  const [newsData, setNewsData] = useState(mockNewsData);
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // 模拟新快讯数据
        const newItem = {
          id: Date.now(),
          time: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
          content: '模拟新快讯：市场动态更新...',
          importance: 'medium' as const,
          category: '市场',
          isNew: true,
        };

        setNewsData(prev => [newItem, ...prev.slice(0, 19)]); // 保持最新20条
      }, 30000); // 30秒更新一次

      return () => clearInterval(interval);
    }

    return undefined;
  }, [autoRefresh]);

  const getImportanceIcon = (importance: string) => {
    if (importance === 'high') return <FireOutlined />;
    if (importance === 'medium') return <ThunderboltOutlined />;
    return <ClockCircleOutlined />;
  };

  return (
    <Container>
      <Header>
        <div>
          <Title 
            level={2} 
            style={{ 
              color: colors.text.primary, 
              margin: 0,
              fontSize: typography.h2.fontSize,
              fontWeight: typography.h2.fontWeight,
            }}
          >
            <ThunderboltOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
            市场快讯
          </Title>
          <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
            实时财经资讯，快速掌握市场动态
          </Text>
        </div>
        <Badge 
          count="LIVE" 
          style={{ 
            backgroundColor: colors.error,
            color: colors.text.primary,
            fontWeight: 'bold',
          }} 
        />
      </Header>

      <ControlPanel>
        <Button
          type={isVoiceEnabled ? 'primary' : 'default'}
          icon={<SoundOutlined />}
          onClick={() => setIsVoiceEnabled(!isVoiceEnabled)}
          style={{
            background: isVoiceEnabled ? colors.primary : colors.background.secondary,
            borderColor: colors.border.default,
            color: isVoiceEnabled ? colors.text.primary : colors.text.secondary,
          }}
        >
          语音播报
        </Button>
        
        <Button
          type={autoRefresh ? 'primary' : 'default'}
          onClick={() => setAutoRefresh(!autoRefresh)}
          style={{
            background: autoRefresh ? colors.primary : colors.background.secondary,
            borderColor: colors.border.default,
            color: autoRefresh ? colors.text.primary : colors.text.secondary,
          }}
        >
          自动刷新
        </Button>

        <Button
          icon={<SettingOutlined />}
          style={{
            background: colors.background.secondary,
            borderColor: colors.border.default,
            color: colors.text.secondary,
          }}
        >
          设置
        </Button>
      </ControlPanel>

      <NewsContainer>
        <Card
          style={{
            background: colors.background.card,
            border: `1px solid ${colors.border.default}`,
            borderRadius: borderRadius.large,
            height: '100%',
          }}
          bodyStyle={{ padding: 0, height: '100%' }}
        >
          <div style={{ height: '100%', overflowY: 'auto' }}>
            {newsData.map((item) => (
              <NewsItem 
                key={item.id} 
                importance={item.importance}
                isNew={item.isNew}
              >
                <Space align="start" style={{ width: '100%' }}>
                  <ImportanceIcon importance={item.importance}>
                    {getImportanceIcon(item.importance)}
                  </ImportanceIcon>
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <TimeStamp>{item.time}</TimeStamp>
                      {item.isNew && (
                        <Badge 
                          count="NEW" 
                          style={{ 
                            backgroundColor: colors.primary,
                            fontSize: '10px',
                            height: '16px',
                            lineHeight: '16px',
                          }} 
                        />
                      )}
                    </div>
                    <NewsContent>{item.content}</NewsContent>
                    <CategoryTag>{item.category}</CategoryTag>
                  </div>
                </Space>
              </NewsItem>
            ))}
          </div>
        </Card>
      </NewsContainer>
    </Container>
  );
};

export default NewsFlash;
