/**
 * 财经日历页面
 */
import React, { useState } from 'react';
import { Card, Calendar as Ant<PERSON>alendar, Badge, Typography, Tag } from 'antd';
import { CalendarOutlined, ClockCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import type { Dayjs } from 'dayjs';

import { colors, spacing, typography, borderRadius } from '@styles/theme';

const { Title, Text } = Typography;

// 模拟财经事件数据
const mockEvents = {
  '2025-08-02': [
    {
      id: 1,
      time: '20:30',
      event: '美国7月非农就业人数',
      importance: 'high',
      previous: '20.6万',
      forecast: '18.5万',
      actual: '20.9万',
    },
    {
      id: 2,
      time: '22:00',
      event: '美联储主席鲍威尔讲话',
      importance: 'high',
      previous: '-',
      forecast: '-',
      actual: '-',
    },
  ],
  '2025-08-03': [
    {
      id: 3,
      time: '09:30',
      event: '中国7月财新制造业PMI',
      importance: 'medium',
      previous: '51.8',
      forecast: '51.5',
      actual: '-',
    },
  ],
  '2025-08-05': [
    {
      id: 4,
      time: '15:00',
      event: '欧元区7月零售销售月率',
      importance: 'medium',
      previous: '0.1%',
      forecast: '0.2%',
      actual: '-',
    },
  ],
};

const Container = styled.div`
  padding: 0;
  height: calc(100vh - 180px);
  display: flex;
  gap: ${spacing.lg};
`;

const CalendarSection = styled.div`
  flex: 1;
`;

const EventsSection = styled.div`
  width: 400px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  margin-bottom: ${spacing.lg};
`;

const EventItem = styled.div<{ importance: string }>`
  padding: ${spacing.md};
  border-left: ${props => {
    if (props.importance === 'high') return `4px solid ${colors.error}`;
    if (props.importance === 'medium') return `4px solid ${colors.warning}`;
    return `4px solid ${colors.info}`;
  }};
  border-bottom: 1px solid ${colors.border.default};
  transition: all 0.3s ease;

  &:hover {
    background: ${colors.background.secondary};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const EventTime = styled.span`
  color: ${colors.text.secondary};
  font-size: ${typography.small.fontSize};
  font-weight: 500;
`;

const EventTitle = styled.div`
  color: ${colors.text.primary};
  font-size: ${typography.body.fontSize};
  font-weight: 500;
  margin: ${spacing.xs} 0;
`;

const DataRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: ${spacing.xs};
  font-size: ${typography.small.fontSize};
`;

const DataLabel = styled.span`
  color: ${colors.text.secondary};
`;

const DataValue = styled.span<{ type: 'previous' | 'forecast' | 'actual' }>`
  color: ${props => {
    if (props.type === 'actual') return colors.primary;
    if (props.type === 'forecast') return colors.warning;
    return colors.text.tertiary;
  }};
  font-weight: 500;
`;

/**
 * 财经日历页面组件
 */
const Calendar: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<string>('2025-08-02');

  const getListData = (value: Dayjs) => {
    const dateStr = value.format('YYYY-MM-DD');
    const events = mockEvents[dateStr as keyof typeof mockEvents] || [];
    
    return events.map(event => ({
      type: event.importance === 'high' ? 'error' : 
            event.importance === 'medium' ? 'warning' : 'default',
      content: event.event,
    }));
  };

  const dateCellRender = (value: Dayjs) => {
    const listData = getListData(value);
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge 
              status={item.type as 'error' | 'warning' | 'default'}
              text={
                <span style={{ 
                  fontSize: '10px', 
                  color: colors.text.secondary,
                  display: 'block',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: '80px',
                }}>
                  {item.content}
                </span>
              } 
            />
          </li>
        ))}
      </ul>
    );
  };

  const onDateSelect = (value: Dayjs) => {
    setSelectedDate(value.format('YYYY-MM-DD'));
  };

  const selectedEvents = mockEvents[selectedDate as keyof typeof mockEvents] || [];

  const getImportanceTag = (importance: string) => {
    const config = {
      high: { color: colors.error, text: '重要' },
      medium: { color: colors.warning, text: '中等' },
      low: { color: colors.info, text: '一般' },
    };
    
    const { color, text } = config[importance as keyof typeof config] || config.low;
    
    return (
      <Tag style={{ 
        backgroundColor: `${color}20`, 
        color: color, 
        border: `1px solid ${color}40`,
        fontSize: '10px',
      }}>
        {text}
      </Tag>
    );
  };

  return (
    <Container>
      <CalendarSection>
        <Header>
          <Title 
            level={2} 
            style={{ 
              color: colors.text.primary, 
              margin: 0,
              fontSize: typography.h2.fontSize,
              fontWeight: typography.h2.fontWeight,
            }}
          >
            <CalendarOutlined style={{ marginRight: spacing.sm, color: colors.primary }} />
            财经日历
          </Title>
          <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
            重要经济数据发布时间表
          </Text>
        </Header>

        <Card
          style={{
            background: colors.background.card,
            border: `1px solid ${colors.border.default}`,
            borderRadius: borderRadius.large,
          }}
        >
          <AntCalendar
            dateCellRender={dateCellRender}
            onSelect={onDateSelect}
            style={{
              background: 'transparent',
            }}
          />
        </Card>
      </CalendarSection>

      <EventsSection>
        <Header>
          <Title 
            level={3} 
            style={{ 
              color: colors.text.primary, 
              margin: 0,
              fontSize: typography.h3.fontSize,
              fontWeight: typography.h3.fontWeight,
            }}
          >
            {selectedDate} 事件
          </Title>
          <Text style={{ color: colors.text.secondary, fontSize: typography.small.fontSize }}>
            当日重要财经事件
          </Text>
        </Header>

        <Card
          style={{
            background: colors.background.card,
            border: `1px solid ${colors.border.default}`,
            borderRadius: borderRadius.large,
            flex: 1,
          }}
          bodyStyle={{ padding: 0, height: '100%' }}
        >
          {selectedEvents.length > 0 ? (
            <div style={{ height: '100%', overflowY: 'auto' }}>
              {selectedEvents.map((event) => (
                <EventItem key={event.id} importance={event.importance}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <EventTime>
                      <ClockCircleOutlined style={{ marginRight: spacing.xs }} />
                      {event.time}
                    </EventTime>
                    {getImportanceTag(event.importance)}
                  </div>
                  
                  <EventTitle>{event.event}</EventTitle>
                  
                  <DataRow>
                    <DataLabel>前值:</DataLabel>
                    <DataValue type="previous">{event.previous}</DataValue>
                  </DataRow>
                  
                  <DataRow>
                    <DataLabel>预期:</DataLabel>
                    <DataValue type="forecast">{event.forecast}</DataValue>
                  </DataRow>
                  
                  <DataRow>
                    <DataLabel>实际:</DataLabel>
                    <DataValue type="actual">{event.actual}</DataValue>
                  </DataRow>
                </EventItem>
              ))}
            </div>
          ) : (
            <div style={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              color: colors.text.tertiary,
            }}>
              <CalendarOutlined style={{ fontSize: '48px', marginBottom: spacing.md, opacity: 0.5 }} />
              <Text style={{ color: colors.text.tertiary }}>当日暂无重要事件</Text>
            </div>
          )}
        </Card>
      </EventsSection>
    </Container>
  );
};

export default Calendar;
