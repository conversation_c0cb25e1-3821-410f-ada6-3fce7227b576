/**
 * 顶部导航组件
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Space, Badge, Button } from 'antd';
import {
  SettingOutlined,
  ThunderboltOutlined,
  CalendarOutlined,
  FileTextOutlined,
  TagsOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';

import { ROUTES } from '@constants/index';
import { colors, typography, spacing, borderRadius, breakpoints } from '@styles/theme';

const { Header } = Layout;

/**
 * 菜单项配置
 */
const menuItems = [
  {
    key: ROUTES.NEWS_FLASH,
    icon: <ThunderboltOutlined />,
    label: '快讯',
  },
  {
    key: ROUTES.CALENDAR,
    icon: <CalendarOutlined />,
    label: '日历',
  },
  {
    key: ROUTES.HEADLINES,
    icon: <FileTextOutlined />,
    label: '头条',
  },
  {
    key: ROUTES.TOPICS,
    icon: <TagsOutlined />,
    label: '专题',
  },
  {
    key: ROUTES.SETTINGS,
    icon: <SettingOutlined />,
    label: '设置',
  },
];

/**
 * 用户下拉菜单项
 */
const userMenuItems = [
  {
    key: 'profile',
    icon: <UserOutlined />,
    label: '个人资料',
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '账户设置',
  },
  {
    type: 'divider' as const,
  },
  {
    key: 'logout',
    icon: <LogoutOutlined />,
    label: '退出登录',
    danger: true,
  },
];

const StyledHeader = styled(Header)`
  background: ${colors.background.card};
  border-bottom: 1px solid ${colors.border.default};
  padding: 0 ${spacing.lg};
  height: 64px;
  line-height: 1; // 重置line-height，避免影响垂直居中
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;

  @media (max-width: ${breakpoints.tablet}) {
    padding: 0 ${spacing.md};
  }

  @media (max-width: ${breakpoints.mobile}) {
    padding: 0 ${spacing.sm};
    height: 56px;
  }
`;

const LogoSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.lg};

  @media (max-width: ${breakpoints.tablet}) {
    gap: ${spacing.md};
  }

  @media (max-width: ${breakpoints.mobile}) {
    gap: ${spacing.sm};
    flex: 1;
    min-width: 0;
  }
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  font-size: ${typography.h3.fontSize};
  font-weight: 700;
  color: ${colors.primary};
  background: linear-gradient(45deg, ${colors.primary}, ${colors.info});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;

  @media (max-width: ${breakpoints.mobile}) {
    font-size: ${typography.body.fontSize};
  }
`;

const NavigationMenu = styled(Menu)`
  background: transparent;
  border: none;
  flex: 1;
  max-width: 600px;

  .ant-menu-item {
    color: ${colors.text.secondary};
    border-radius: ${borderRadius.small};
    margin: 0 ${spacing.xs};
    height: 40px;
    line-height: 1; // 重置line-height
    display: flex !important;
    align-items: center !important;
    justify-content: center;

    &:hover {
      color: ${colors.primary};
      background: rgba(79, 70, 229, 0.1);
    }

    &.ant-menu-item-selected {
      color: ${colors.primary};
      background: rgba(79, 70, 229, 0.15);

      &::after {
        border-bottom: 2px solid ${colors.primary};
      }
    }
  }

  .ant-menu-item-icon {
    font-size: 16px;
    margin-right: ${spacing.xs};
  }

  .ant-menu-title-content {
    font-size: ${typography.small.fontSize};
    font-weight: 500;
  }

  @media (max-width: ${breakpoints.tablet}) {
    max-width: 400px;

    .ant-menu-item {
      margin: 0 2px;
      padding: 0 8px;
    }

    .ant-menu-title-content {
      font-size: 12px;
    }
  }

  @media (max-width: ${breakpoints.mobile}) {
    display: none; // 移动端隐藏导航菜单
  }
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${spacing.md};

  @media (max-width: ${breakpoints.tablet}) {
    gap: ${spacing.sm};
  }

  @media (max-width: ${breakpoints.mobile}) {
    gap: ${spacing.xs};
  }
`;

const TimeDisplay = styled.div`
  color: ${colors.text.secondary};
  font-size: ${typography.small.fontSize};
  font-weight: 500;
  white-space: nowrap;

  @media (max-width: ${breakpoints.mobile}) {
    display: none; // 移动端隐藏时间显示
  }
`;

const NotificationButton = styled(Button)`
  background: transparent;
  border: 1px solid ${colors.border.default};
  color: ${colors.text.secondary};
  
  &:hover {
    border-color: ${colors.primary};
    color: ${colors.primary};
  }
`;

/**
 * 顶部导航组件
 */
const TopNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [currentTime, setCurrentTime] = useState(new Date());


  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        // 处理个人资料
        break;
      case 'settings':
        navigate(ROUTES.SETTINGS);
        break;
      case 'logout':
        // 处理退出登录
        localStorage.clear();
        navigate(ROUTES.LOGIN);
        break;
      default:
        break;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { hour12: false });
  };

  const selectedKeys = [location.pathname];

  return (
    <StyledHeader>
      <LogoSection>
        <Logo>
          FinSight
        </Logo>
        
        <NavigationMenu
          mode="horizontal"
          selectedKeys={selectedKeys}
          onClick={handleMenuClick}
          items={menuItems}
          style={{ minWidth: 0, flex: 1 }}
        />
      </LogoSection>

      <RightSection>
        <TimeDisplay>
          交易时钟：{formatTime(currentTime)}
        </TimeDisplay>
        
        <Badge count={5} size="small">
          <NotificationButton
            icon={<BellOutlined />}
            shape="circle"
          />
        </Badge>

        <Dropdown
          menu={{
            items: userMenuItems,
            onClick: handleUserMenuClick,
          }}
          placement="bottomRight"
          arrow
        >
          <Space style={{ cursor: 'pointer' }}>
            <Avatar
              size="small"
              style={{
                backgroundColor: colors.primary,
                color: colors.text.primary,
              }}
            >
              U
            </Avatar>
          </Space>
        </Dropdown>
      </RightSection>
    </StyledHeader>
  );
};

export default TopNavigation;
