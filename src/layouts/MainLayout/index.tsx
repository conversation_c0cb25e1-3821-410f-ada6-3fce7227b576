/**
 * 主布局组件
 */
import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout } from 'antd';
import styled from 'styled-components';

import TopNavigation from './TopNavigation';
import { colors, spacing, borderRadius, breakpoints } from '@styles/theme';

const { Content } = Layout;

// 响应式容器组件
const ResponsiveContainer = styled.div`
  width: 100%;
  max-width: 1400px; // 大屏最大宽度限制
  margin: 0 auto; // 水平居中
  padding: 0 ${spacing.lg};

  @media (min-width: 1600px) {
    max-width: 1200px; // 超大屏进一步限制宽度
  }

  @media (max-width: ${breakpoints.tablet}) {
    padding: 0 ${spacing.md};
  }

  @media (max-width: ${breakpoints.mobile}) {
    padding: 0 ${spacing.sm};
  }
`;

const StyledContent = styled(Content)`
  margin: ${spacing.lg};
  padding: ${spacing.lg};
  background: ${colors.background.card};
  border-radius: ${borderRadius.large};
  border: 1px solid ${colors.border.default};
  min-height: calc(100vh - 64px - 48px); // 减去header高度和margin
  overflow: auto;

  @media (max-width: ${breakpoints.tablet}) {
    margin: ${spacing.md};
    padding: ${spacing.md};
    min-height: calc(100vh - 64px - 32px);
  }

  @media (max-width: ${breakpoints.mobile}) {
    margin: ${spacing.sm};
    padding: ${spacing.sm};
    border-radius: ${borderRadius.medium};
    min-height: calc(100vh - 64px - 16px);
  }
`;

/**
 * 主布局组件
 */
const MainLayout: React.FC = () => {
  return (
    <Layout
      style={{
        minHeight: '100vh',
        background: colors.background.primary,
      }}
    >
      <TopNavigation />
      <ResponsiveContainer>
        <StyledContent>
          <Outlet />
        </StyledContent>
      </ResponsiveContainer>
    </Layout>
  );
};

export default MainLayout;
